apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-spring-backend-dev
  namespace: argocd
  labels:
    app: ai-spring-backend
    environment: dev
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/managed-by: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "0"
spec:
  project: default
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps.git
    targetRevision: HEAD
    path: ai-spring-backend/k8s/overlays/dev
  destination:
    server: https://kubernetes.default.svc
    namespace: ai-spring-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10