apiVersion: v1
kind: ConfigMap
metadata:
  name: AI Spring Backend-config
  namespace: ai-spring-backend-dev
  labels:
    app: AI Spring Backend
    app.kubernetes.io/name: AI Spring Backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: AI Spring Backend
    environment: dev
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8080"
  AI Spring Backend: "AI Spring Backend"
  springboot-backend: "springboot-backend"

  # Database Configuration (if enabled)
  DB_HOST: "DB_HOST"
  DB_PORT: "DB_PORT"
  DB_NAME: "DB_NAME"
  DB_USER: "DB_USER"

  # SMTP Configuration
  SMTP_HOST: "SMTP_HOST"
  SMTP_PORT: "SMTP_PORT"
  SMTP_FROM: "SMTP_FROM"

  # Dynamic Backend Configuration (for frontend applications)
  API_URL: "API_URL"
  BACKEND_TYPE: "BACKEND_TYPE"
  BACKEND_PROJECT_ID: "BACKEND_PROJECT_ID"
  BACKEND_PORT: "BACKEND_PORT"
  BACKEND_NAMESPACE: "BACKEND_NAMESPACE"

  # Frontend-specific environment variables
  REACT_APP_API_URL: "API_URL"
  REACT_APP_BACKEND_TYPE: "BACKEND_TYPE"
  VUE_APP_API_URL: "API_URL"
  VUE_APP_BACKEND_TYPE: "BACKEND_TYPE"
  ANGULAR_API_URL: "API_URL"
  ANGULAR_BACKEND_TYPE: "BACKEND_TYPE"

  # Service Discovery Configuration
  SERVICE_NAME: "AI Spring Backend-service"
  SERVICE_NAMESPACE: "ai-spring-backend-dev"
  SERVICE_PORT: "8080"

  # Application-specific configurations will be added by overlays
