apiVersion: apps/v1
kind: Deployment
metadata:
  name: AI Spring Backend
  namespace: ai-spring-backend-dev
  labels:
    app: AI Spring Backend
    app.kubernetes.io/name: AI Spring Backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: AI Spring Backend
    environment: dev
    deployment.strategy: rolling-update
  annotations:
    deployment.kubernetes.io/revision: "1"
    argocd.argoproj.io/sync-wave: "2"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 50%
      maxSurge: 50%
  progressDeadlineSeconds: 120
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: AI Spring Backend
      app.kubernetes.io/name: AI Spring Backend
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: AI Spring Backend
        app.kubernetes.io/name: AI Spring Backend
        app.kubernetes.io/component: springboot-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        app.kubernetes.io/part-of: AI Spring Backend
        environment: dev
        deployment.strategy: rolling-update
    spec:
      securityContext:
        runAsNonRoot: SECURITY_RUN_AS_NON_ROOT
        runAsUser: SECURITY_RUN_AS_USER
        runAsGroup: SECURITY_RUN_AS_GROUP
        fsGroup: SECURITY_FS_GROUP
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      containers:
      - name: AI Spring Backend
        image: CONTAINER_IMAGE
        imagePullPolicy: Always
        securityContext:
          runAsNonRoot: SECURITY_RUN_AS_NON_ROOT
          runAsUser: SECURITY_RUN_AS_USER
          runAsGroup: SECURITY_RUN_AS_GROUP
          readOnlyRootFilesystem: SECURITY_READ_ONLY_ROOT_FS
          allowPrivilegeEscalation: SECURITY_ALLOW_PRIVILEGE_ESCALATION
          capabilities:
            drop: [SECURITY_CAPABILITIES_DROP]
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: STARTUP_PROBE_INITIAL_DELAY
          periodSeconds: STARTUP_PROBE_PERIOD
          timeoutSeconds: STARTUP_PROBE_TIMEOUT
          failureThreshold: STARTUP_PROBE_FAILURE_THRESHOLD
          successThreshold: STARTUP_PROBE_SUCCESS_THRESHOLD
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        env:
        - name: NODE_ENV
          value: dev
        - name: PORT
          value: "8080"
        envFrom:
        - configMapRef:
            name: AI Spring Backend-config
        - secretRef:
            name: AI Spring Backend-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
