apiVersion: v1
kind: Service
metadata:
  name: AI Spring Backend-service
  namespace: ai-spring-backend-dev
  labels:
    app: AI Spring Backend
    app.kubernetes.io/name: AI Spring Backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: AI Spring Backend
    environment: dev
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  type: LoadBalancer
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: AI Spring Backend
    app.kubernetes.io/name: AI Spring Backend
    app.kubernetes.io/managed-by: argocd
